'use strict';

const { Sequelize } = require('sequelize');
const config = require('./database/config.json');

// 获取开发环境配置
const env = process.env.NODE_ENV || 'development';
const dbConfig = config[env];

// 创建 Sequelize 实例
const sequelize = new Sequelize(
  dbConfig.database,
  dbConfig.username,
  dbConfig.password,
  {
    host: dbConfig.host,
    dialect: dbConfig.dialect,
    logging: console.log, // 显示 SQL 查询日志
  }
);

// 测试连接
async function testConnection() {
  try {
    await sequelize.authenticate();
    console.log('数据库连接成功！');
  } catch (error) {
    console.error('数据库连接失败:', error);
  } finally {
    await sequelize.close();
  }
}

testConnection();