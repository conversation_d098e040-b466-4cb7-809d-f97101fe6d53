'use strict';

const { Sequelize } = require('sequelize');
const config = require('./database/config.json');

// 获取当前环境配置
const env = process.env.NODE_ENV || 'development';
const dbConfig = config[env];

// 验证配置是否存在
if (!dbConfig) {
  throw new Error(`数据库配置不存在: ${env}`);
}

// 创建 Sequelize 实例
const sequelize = new Sequelize(
  dbConfig.database,
  dbConfig.username,
  dbConfig.password,
  {
    host: dbConfig.host,
    port: dbConfig.port || 3306,
    dialect: dbConfig.dialect,
    // 根据环境设置日志
    logging: env === 'production' ? false : console.log,
    // 连接池配置
    pool: {
      max: 10,
      min: 0,
      acquire: 30000,
      idle: 10000,
    },
    // 时区设置
    timezone: '+08:00',
    // 其他配置
    define: {
      timestamps: true,
      underscored: true,
      freezeTableName: true,
    },
  }
);

// 测试数据库连接的函数（可选使用）
async function testConnection() {
  try {
    await sequelize.authenticate();
    console.log(`数据库连接成功！环境: ${env}`);
    return true;
  } catch (error) {
    console.error('数据库连接失败:', error);
    return false;
  }
}

// 导出 sequelize 实例和测试函数
module.exports = {
  sequelize,
  testConnection,
  Sequelize,
};