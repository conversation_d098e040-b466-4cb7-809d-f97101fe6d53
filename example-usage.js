'use strict';

// 使用示例：如何在项目中使用 database.js

const { sequelize, testConnection, Sequelize } = require('./database');

// 示例 1: 测试数据库连接
async function checkDatabaseConnection() {
  console.log('正在测试数据库连接...');
  const isConnected = await testConnection();
  if (isConnected) {
    console.log('✅ 数据库连接正常');
  } else {
    console.log('❌ 数据库连接失败');
  }
}

// 示例 2: 定义一个简单的模型
const User = sequelize.define('User', {
  id: {
    type: Sequelize.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  name: {
    type: Sequelize.STRING,
    allowNull: false,
  },
  email: {
    type: Sequelize.STRING,
    allowNull: false,
    unique: true,
  },
  created_at: {
    type: Sequelize.DATE,
    defaultValue: Sequelize.NOW,
  },
});

// 示例 3: 使用模型进行数据库操作
async function userOperations() {
  try {
    // 同步模型到数据库（开发环境使用）
    await User.sync({ force: false });
    
    // 创建用户
    const newUser = await User.create({
      name: '张三',
      email: '<EMAIL>',
    });
    console.log('创建用户成功:', newUser.toJSON());
    
    // 查询用户
    const users = await User.findAll();
    console.log('所有用户:', users.map(user => user.toJSON()));
    
    // 查询单个用户
    const user = await User.findOne({ where: { email: '<EMAIL>' } });
    if (user) {
      console.log('找到用户:', user.toJSON());
    }
    
  } catch (error) {
    console.error('用户操作失败:', error);
  }
}

// 示例 4: 执行原生 SQL 查询
async function rawQuery() {
  try {
    const [results, metadata] = await sequelize.query('SELECT NOW() as current_time');
    console.log('当前时间:', results[0].current_time);
  } catch (error) {
    console.error('原生查询失败:', error);
  }
}

// 主函数
async function main() {
  await checkDatabaseConnection();
  await userOperations();
  await rawQuery();
  
  // 关闭数据库连接
  await sequelize.close();
  console.log('数据库连接已关闭');
}

// 如果直接运行此文件，则执行示例
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  User,
  checkDatabaseConnection,
  userOperations,
  rawQuery,
};
